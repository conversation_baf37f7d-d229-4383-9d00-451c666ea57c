<x-filament-panels::page>
    <!-- Service Status Card -->
    <div class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Status WhatsApp Service
                </h3>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {{ $isServiceRunning ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $isServiceRunning ? 'Running' : 'Stopped' }}
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {{ $serviceStatus === 'connected' ? 'bg-green-100 text-green-800' :
                           ($serviceStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                        {{ ucfirst($serviceStatus) }}
                    </span>
                </div>
            </div>

            <!-- QR Code Section Selalu Ada -->
            <div class="border-t pt-4">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">
                    Scan QR Code untuk Menghubungkan WhatsApp
                </h4>
                <div class="flex justify-center">
                    <div class="bg-white p-4 rounded-lg shadow-inner">
                        <div id="qrcode" class="flex justify-center"></div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 text-center mt-3">
                    @if($serviceStatus === 'connected')
                        ✅ <strong>WhatsApp Terhubung!</strong> Service siap mengirim notifikasi otomatis
                    @elseif($serviceStatus === 'connecting')
                        🔄 Service sedang memulai (npm run dev), QR code akan muncul sebentar lagi...
                    @elseif($isServiceRunning)
                        ⏳ Service berjalan, menunggu koneksi WhatsApp. Scan QR code untuk menghubungkan.
                    @else
                        ❌ Service tidak aktif. Aktifkan toggle di atas untuk memulai service otomatis.
                    @endif
                </p>

                @if($isServiceRunning && $serviceStatus !== 'connected')
                <div class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p class="text-sm text-blue-700 dark:text-blue-300 text-center">
                        📱 <strong>Cara Scan QR Code:</strong><br>
                        Buka WhatsApp di ponsel → Ketuk menu (⋮) → WhatsApp Web → Scan QR code di atas
                    </p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <x-filament-panels::form wire:submit="submit">
        {{ $this->form }}
    </x-filament-panels::form>

    <!-- Auto-refresh QR Code -->
    <!-- Ganti ke versi 1.x agar window.QRCode tersedia (class-based) -->
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        let lastQR = null;
        function renderQR(qrElement, text) {
            if (typeof QRCode === 'function' && typeof QRCode.toCanvas === 'function') {
                QRCode.toCanvas(qrElement, text, {
                    width: 256,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });
            } else if (typeof window.QRCode === 'function') {
                new window.QRCode(qrElement, {
                    text: text,
                    width: 256,
                    height: 256,
                    colorDark: '#000000',
                    colorLight: '#FFFFFF',
                    correctLevel: window.QRCode.CorrectLevel.H
                });
            } else {
                alert('Library QRCode.js belum termuat!');
            }
        }

        function fetchAndRenderQR() {
            fetch('http://localhost:3001/status')
                .then(res => res.json())
                .then(data => {
                    const qrElement = document.getElementById('qrcode');
                    if (qrElement) {
                        if (data.qr && (typeof QRCode !== 'undefined' || typeof window.QRCode !== 'undefined')) {
                            if (lastQR !== data.qr) {
                                qrElement.innerHTML = '';
                                renderQR(qrElement, data.qr);
                                lastQR = data.qr;
                            }
                        } else if (typeof QRCode === 'undefined' && typeof window.QRCode === 'undefined') {
                            setTimeout(fetchAndRenderQR, 1000);
                        } else {
                            qrElement.innerHTML = '';
                            lastQR = null;
                        }
                    }
                });
        }
        fetchAndRenderQR();
        setInterval(fetchAndRenderQR, 5000);
    });
    </script>
</x-filament-panels::page>