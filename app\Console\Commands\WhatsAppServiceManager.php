<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\WhatsAppProcessService;
use App\Services\WhatsAppService;

class WhatsAppServiceManager extends Command
{
    protected $signature = 'whatsapp:service {action : start|stop|restart|status}';
    protected $description = 'Manage WhatsApp service (start/stop/restart/status)';

    public function handle()
    {
        $action = $this->argument('action');
        $processService = app(WhatsAppProcessService::class);
        $whatsappService = app(WhatsAppService::class);

        switch ($action) {
            case 'start':
                $this->info('Starting WhatsApp service...');
                $result = $processService->startService();
                
                if ($result['success']) {
                    $this->info('✅ ' . $result['message']);
                    if (isset($result['pid'])) {
                        $this->info("Process ID: {$result['pid']}");
                    }
                } else {
                    $this->error('❌ ' . $result['error']);
                    return 1;
                }
                break;

            case 'stop':
                $this->info('Stopping WhatsApp service...');
                $result = $processService->stopService();
                
                if ($result['success']) {
                    $this->info('✅ ' . $result['message']);
                } else {
                    $this->error('❌ ' . $result['error']);
                    return 1;
                }
                break;

            case 'restart':
                $this->info('Restarting WhatsApp service...');
                $result = $processService->restartService();
                
                if ($result['success']) {
                    $this->info('✅ ' . $result['message']);
                } else {
                    $this->error('❌ ' . $result['error']);
                    return 1;
                }
                break;

            case 'status':
                $this->info('Checking WhatsApp service status...');
                
                // Check process status
                $processStatus = $processService->getStatus();
                $this->table(['Property', 'Value'], [
                    ['Process Running', $processStatus['process_running'] ? '✅ Yes' : '❌ No'],
                    ['Connection Status', $processStatus['connection_status']],
                    ['Connected', $processStatus['connected'] ? '✅ Yes' : '❌ No'],
                    ['QR Code Available', isset($processStatus['qr_code']) && $processStatus['qr_code'] ? '✅ Yes' : '❌ No'],
                    ['PID', $processStatus['pid'] ?? 'N/A'],
                    ['Timestamp', $processStatus['timestamp']],
                ]);

                // Check API status
                $this->info('Checking API connectivity...');
                try {
                    $apiStatus = $whatsappService->getStatus();
                    $this->table(['API Property', 'Value'], [
                        ['Status', $apiStatus['status'] ?? 'N/A'],
                        ['Connected', isset($apiStatus['connected']) && $apiStatus['connected'] ? '✅ Yes' : '❌ No'],
                        ['Error', $apiStatus['error'] ?? 'None'],
                    ]);
                } catch (\Exception $e) {
                    $this->error('API Error: ' . $e->getMessage());
                }
                break;

            default:
                $this->error('Invalid action. Use: start, stop, restart, or status');
                return 1;
        }

        return 0;
    }
}
