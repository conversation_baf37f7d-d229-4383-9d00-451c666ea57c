<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Symfony\Component\Process\Process as SymfonyProcess;

class WhatsAppProcessService
{
    private $process = null;
    private $isRunning = false;
    private $qrCode = null;
    private $connectionStatus = 'disconnected';
    private $pidFile = null;

    public function __construct()
    {
        $this->pidFile = storage_path('app/whatsapp-service.pid');
        $this->checkExistingProcess();
    }

    /**
     * Start WhatsApp service
     */
    public function startService(): array
    {
        try {
            if ($this->isRunning) {
                return [
                    'success' => true,
                    'message' => 'Service already running',
                    'status' => $this->connectionStatus
                ];
            }

            $whatsappPath = base_path('whatsapp-service');
            if (!is_dir($whatsappPath)) {
                return [
                    'success' => false,
                    'error' => 'WhatsApp service directory not found'
                ];
            }

            // Kill any existing processes first
            $this->killExistingProcesses();
            sleep(1);

            // Start Node.js process directly
            $command = $this->getStartCommand();

            $this->process = new SymfonyProcess(
                $command,
                $whatsappPath,
                null,
                null,
                null // No timeout - let it run indefinitely
            );

            $this->process->start();

            // Save PID for later reference
            if ($this->process->isRunning()) {
                file_put_contents($this->pidFile, $this->process->getPid());
                Log::info('WhatsApp service started', [
                    'pid' => $this->process->getPid(),
                    'command' => $command
                ]);

                // Wait a moment for service to initialize
                sleep(3);
                $this->isRunning = true;

                return [
                    'success' => true,
                    'message' => 'WhatsApp service started successfully',
                    'status' => 'starting',
                    'pid' => $this->process->getPid()
                ];
            } else {
                Log::error('Failed to start WhatsApp service - process not running');
                return [
                    'success' => false,
                    'error' => 'Failed to start WhatsApp service - process not running'
                ];
            }

        } catch (\Exception $e) {
            Log::error('Failed to start WhatsApp service', [
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get the appropriate start command based on OS and available tools
     */
    private function getStartCommand(): array
    {
        // Check if PM2 is available and ecosystem.config.js exists
        if ($this->isPM2Available() && file_exists(base_path('whatsapp-service/ecosystem.config.js'))) {
            return $this->getPM2StartCommand();
        }

        // Fallback to direct npm command
        if (PHP_OS_FAMILY === 'Windows') {
            return ['cmd', '/c', 'npm run dev'];
        } else {
            return ['npm', 'run', 'dev'];
        }
    }

    /**
     * Check if PM2 is available
     */
    private function isPM2Available(): bool
    {
        try {
            $result = Process::run('pm2 --version');
            return $result->successful();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get PM2 start command
     */
    private function getPM2StartCommand(): array
    {
        if (PHP_OS_FAMILY === 'Windows') {
            return ['cmd', '/c', 'pm2 start ecosystem.config.js --only whatsapp-service'];
        } else {
            return ['pm2', 'start', 'ecosystem.config.js', '--only', 'whatsapp-service'];
        }
    }

    /**
     * Stop WhatsApp service
     */
    public function stopService(): array
    {
        try {
            $stopped = false;

            // Try to stop the current process if it exists
            if ($this->process && $this->process->isRunning()) {
                $this->process->stop(3, SIGTERM); // Give 3 seconds to gracefully shutdown
                if (!$this->process->isRunning()) {
                    $stopped = true;
                    Log::info('WhatsApp service stopped gracefully');
                }
            }

            // If graceful stop didn't work, kill all related processes
            if (!$stopped) {
                $this->killExistingProcesses();
                $stopped = true;
                Log::info('WhatsApp service stopped forcefully');
            }

            // Clean up
            $this->isRunning = false;
            $this->qrCode = null;
            $this->connectionStatus = 'disconnected';
            $this->process = null;

            // Remove PID file
            if (file_exists($this->pidFile)) {
                unlink($this->pidFile);
            }

            return [
                'success' => true,
                'message' => 'WhatsApp service stopped successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to stop WhatsApp service', [
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get service status and QR code
     */
    public function getStatus(): array
    {
        try {
            // Check if process is still running
            if ($this->process && !$this->process->isRunning()) {
                $this->isRunning = false;
            }

            // Try to get status from WhatsApp API to determine if service is actually running
            $whatsappService = app(WhatsAppService::class);
            $apiStatus = $whatsappService->getStatus();

            // If API is accessible, service is running
            if (isset($apiStatus['status']) || isset($apiStatus['connected'])) {
                $this->isRunning = true;
                $this->connectionStatus = $apiStatus['status'] ?? ($apiStatus['connected'] ? 'connected' : 'disconnected');
                $this->qrCode = $apiStatus['qr'] ?? null;
            } else {
                // API not accessible, service is not running
                $this->isRunning = false;
                $this->connectionStatus = 'disconnected';
                $this->qrCode = null;
            }

            return [
                'process_running' => $this->isRunning,
                'connection_status' => $this->connectionStatus,
                'connected' => $apiStatus['connected'] ?? false,
                'qr_code' => $this->qrCode,
                'pid' => $this->process ? $this->process->getPid() : null,
                'timestamp' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            // If we can't connect to API, service is not running
            $this->isRunning = false;
            $this->connectionStatus = 'disconnected';
            $this->qrCode = null;

            return [
                'process_running' => false,
                'connection_status' => 'disconnected',
                'connected' => false,
                'qr_code' => null,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Check if there's already a running process
     */
    private function checkExistingProcess(): void
    {
        try {
            // Check if PID file exists and process is running
            if (file_exists($this->pidFile)) {
                $pid = (int) file_get_contents($this->pidFile);
                if ($this->isProcessRunning($pid)) {
                    $this->isRunning = true;
                    Log::info('Found existing WhatsApp service process', ['pid' => $pid]);
                } else {
                    // PID file exists but process is dead, clean up
                    unlink($this->pidFile);
                }
            }

            // Double-check by trying to connect to the API
            $whatsappService = app(WhatsAppService::class);
            $status = $whatsappService->getStatus();

            if (isset($status['status']) && $status['status'] !== 'error') {
                $this->isRunning = true;
                $this->connectionStatus = $status['status'] ?? 'connected';
                $this->qrCode = $status['qr'] ?? null;
            } else if (!$this->isRunning) {
                $this->isRunning = false;
                $this->connectionStatus = 'disconnected';
            }
        } catch (\Exception $e) {
            // Service not running or not accessible
            $this->isRunning = false;
            $this->connectionStatus = 'disconnected';
        }
    }

    /**
     * Check if a process with given PID is running
     */
    private function isProcessRunning(int $pid): bool
    {
        if (PHP_OS_FAMILY === 'Windows') {
            $result = Process::run("tasklist /FI \"PID eq {$pid}\" /FO CSV");
            return $result->successful() && str_contains($result->output(), (string) $pid);
        } else {
            $result = Process::run("ps -p {$pid}");
            return $result->successful();
        }
    }

    /**
     * Kill existing WhatsApp service processes
     */
    private function killExistingProcesses(): void
    {
        try {
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows: Kill node processes
                // Simple approach - kill all node.exe processes
                $result1 = Process::run('taskkill /F /IM node.exe');

                // Try to kill processes using port 3001
                $result2 = Process::run('netstat -ano | findstr :3001');
                if ($result2->successful()) {
                    // Extract PIDs and kill them
                    $output = $result2->output();
                    if (preg_match_all('/\s+(\d+)$/', $output, $matches)) {
                        foreach ($matches[1] as $pid) {
                            Process::run("taskkill /F /PID {$pid}");
                        }
                    }
                }

            } else {
                // Linux/Mac: Kill processes using port 3001
                Process::run('pkill -f "whatsapp-service"');
                Process::run('pkill -f "node.*3001"');

                // Kill by port
                $result = Process::run('lsof -ti:3001');
                if ($result->successful()) {
                    $pids = trim($result->output());
                    if ($pids) {
                        Process::run("kill -9 {$pids}");
                    }
                }
            }

            Log::info('Attempted to kill existing WhatsApp processes');

        } catch (\Exception $e) {
            Log::warning('Failed to kill existing processes', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Restart service
     */
    public function restartService(): array
    {
        $stopResult = $this->stopService();
        sleep(2); // Wait for complete shutdown
        return $this->startService();
    }

    /**
     * Check if service is running
     */
    public function isRunning(): bool
    {
        // Check if our process is still running
        if ($this->process && $this->process->isRunning()) {
            return true;
        }

        // Check if service is accessible via API (more reliable)
        try {
            $whatsappService = app(WhatsAppService::class);
            $status = $whatsappService->getStatus();
            return isset($status['status']) && $status['status'] !== 'error';
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get QR code for scanning
     */
    public function getQrCode(): ?string
    {
        return $this->qrCode;
    }

    /**
     * Start service using PM2 (if available)
     */
    public function startServiceWithPM2(): array
    {
        try {
            if (!$this->isPM2Available()) {
                return [
                    'success' => false,
                    'error' => 'PM2 is not installed or not available'
                ];
            }

            $whatsappPath = base_path('whatsapp-service');
            if (!file_exists($whatsappPath . '/ecosystem.config.js')) {
                return [
                    'success' => false,
                    'error' => 'ecosystem.config.js not found'
                ];
            }

            // Kill existing processes first
            $this->killExistingProcesses();
            sleep(1);

            $cwd = getcwd();
            chdir($whatsappPath);
            $result = Process::run('pm2 start ecosystem.config.js --only whatsapp-service');
            chdir($cwd);

            if ($result->successful()) {
                Log::info('WhatsApp service started via PM2');
                sleep(3);
                $this->isRunning = true;
                return [
                    'success' => true,
                    'message' => 'WhatsApp service started via PM2',
                    'status' => 'starting'
                ];
            } else {
                Log::error('Failed to start WhatsApp service via PM2', [
                    'output' => $result->output()
                ]);
                return [
                    'success' => false,
                    'error' => 'Failed to start WhatsApp service via PM2: ' . $result->output()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Failed to start WhatsApp service via PM2', [
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Stop service using PM2 (if available)
     */
    public function stopServiceWithPM2(): array
    {
        try {
            if (!$this->isPM2Available()) {
                return $this->stopService(); // Fallback to regular stop
            }

            $whatsappPath = base_path('whatsapp-service');
            $cwd = getcwd();
            chdir($whatsappPath);
            $result = Process::run('pm2 stop whatsapp-service');
            chdir($cwd);

            if ($result->successful()) {
                Log::info('WhatsApp service stopped via PM2');
                $this->isRunning = false;
                $this->qrCode = null;
                $this->connectionStatus = 'disconnected';
                $this->process = null;
                return [
                    'success' => true,
                    'message' => 'WhatsApp service stopped via PM2'
                ];
            } else {
                // Fallback to force kill
                return $this->stopService();
            }
        } catch (\Exception $e) {
            // Fallback to regular stop method
            return $this->stopService();
        }
    }
}