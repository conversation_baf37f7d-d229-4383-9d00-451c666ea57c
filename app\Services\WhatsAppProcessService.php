<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Symfony\Component\Process\Process as SymfonyProcess;

class WhatsAppProcessService
{
    private $process = null;
    private $isRunning = false;
    private $qrCode = null;
    private $connectionStatus = 'disconnected';
    private $pidFile = null;

    public function __construct()
    {
        $this->pidFile = storage_path('app/whatsapp-service.pid');
        $this->checkExistingProcess();
    }

    /**
     * Start WhatsApp service
     */
    public function startService(): array
    {
        try {
            if ($this->isRunning) {
                return [
                    'success' => true,
                    'message' => 'Service already running',
                    'status' => $this->connectionStatus
                ];
            }

            $whatsappPath = base_path('whatsapp-service');
            if (!is_dir($whatsappPath)) {
                return [
                    'success' => false,
                    'error' => 'WhatsApp service directory not found'
                ];
            }

            // Jalankan pm2 start di direktori whatsapp-service
            $cwd = getcwd();
            chdir($whatsappPath);
            $result = Process::run('pm2 start ecosystem.config.js --only whatsapp-service');
            chdir($cwd);

            if ($result->successful()) {
                Log::info('WhatsApp service started via PM2');
                sleep(3);
                $this->isRunning = true;
                return [
                    'success' => true,
                    'message' => 'WhatsApp service started via PM2',
                    'status' => 'starting'
                ];
            } else {
                Log::error('Failed to start WhatsApp service via PM2', [
                    'output' => $result->output()
                ]);
                return [
                    'success' => false,
                    'error' => 'Failed to start WhatsApp service via PM2',
                    'output' => $result->output()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Failed to start WhatsApp service', [
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Stop WhatsApp service
     */
    public function stopService(): array
    {
        try {
            $whatsappPath = base_path('whatsapp-service');
            $cwd = getcwd();
            chdir($whatsappPath);
            $result = Process::run('pm2 stop whatsapp-service');
            chdir($cwd);

            if ($result->successful()) {
                Log::info('WhatsApp service stopped via PM2');
                sleep(1);
                $this->isRunning = false;
                $this->qrCode = null;
                $this->connectionStatus = 'disconnected';
                $this->process = null;
                return [
                    'success' => true,
                    'message' => 'WhatsApp service stopped via PM2'
                ];
            } else {
                Log::error('Failed to stop WhatsApp service via PM2', [
                    'output' => $result->output()
                ]);
                return [
                    'success' => false,
                    'error' => 'Failed to stop WhatsApp service via PM2',
                    'output' => $result->output()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Failed to stop WhatsApp service', [
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get service status and QR code
     */
    public function getStatus(): array
    {
        try {
            // Check if process is still running
            if ($this->process && !$this->process->isRunning()) {
                $this->isRunning = false;
            }

            // Try to get status from WhatsApp API to determine if service is actually running
            $whatsappService = app(WhatsAppService::class);
            $apiStatus = $whatsappService->getStatus();

            // If API is accessible, service is running
            if (isset($apiStatus['status']) || isset($apiStatus['connected'])) {
                $this->isRunning = true;
                $this->connectionStatus = $apiStatus['status'] ?? ($apiStatus['connected'] ? 'connected' : 'disconnected');
                $this->qrCode = $apiStatus['qr'] ?? null;
            } else {
                // API not accessible, service is not running
                $this->isRunning = false;
                $this->connectionStatus = 'disconnected';
                $this->qrCode = null;
            }

            return [
                'process_running' => $this->isRunning,
                'connection_status' => $this->connectionStatus,
                'connected' => $apiStatus['connected'] ?? false,
                'qr_code' => $this->qrCode,
                'pid' => $this->process ? $this->process->getPid() : null,
                'timestamp' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            // If we can't connect to API, service is not running
            $this->isRunning = false;
            $this->connectionStatus = 'disconnected';
            $this->qrCode = null;

            return [
                'process_running' => false,
                'connection_status' => 'disconnected',
                'connected' => false,
                'qr_code' => null,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Check if there's already a running process
     */
    private function checkExistingProcess(): void
    {
        try {
            // Check if WhatsApp service is already running on port 3001
            $whatsappService = app(WhatsAppService::class);
            $status = $whatsappService->getStatus();
            
            if (isset($status['connected']) && $status['connected']) {
                $this->isRunning = true;
                $this->connectionStatus = $status['status'] ?? 'connected';
                $this->qrCode = $status['qr'] ?? null;
            }
        } catch (\Exception $e) {
            // Service not running or not accessible
            $this->isRunning = false;
        }
    }

    /**
     * Kill existing WhatsApp service processes
     */
    private function killExistingProcesses(): void
    {
        try {
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows: Kill node processes
                // Simple approach - kill all node.exe processes
                $result1 = Process::run('taskkill /F /IM node.exe');

                // Try to kill processes using port 3001
                $result2 = Process::run('netstat -ano | findstr :3001');
                if ($result2->successful()) {
                    // Extract PIDs and kill them
                    $output = $result2->output();
                    if (preg_match_all('/\s+(\d+)$/', $output, $matches)) {
                        foreach ($matches[1] as $pid) {
                            Process::run("taskkill /F /PID {$pid}");
                        }
                    }
                }

            } else {
                // Linux/Mac: Kill processes using port 3001
                Process::run('pkill -f "whatsapp-service"');
                Process::run('pkill -f "node.*3001"');

                // Kill by port
                $result = Process::run('lsof -ti:3001');
                if ($result->successful()) {
                    $pids = trim($result->output());
                    if ($pids) {
                        Process::run("kill -9 {$pids}");
                    }
                }
            }

            Log::info('Attempted to kill existing WhatsApp processes');

        } catch (\Exception $e) {
            Log::warning('Failed to kill existing processes', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Restart service
     */
    public function restartService(): array
    {
        $stopResult = $this->stopService();
        sleep(2); // Wait for complete shutdown
        return $this->startService();
    }

    /**
     * Check if service is running
     */
    public function isRunning(): bool
    {
        return $this->isRunning && ($this->process ? $this->process->isRunning() : false);
    }

    /**
     * Get QR code for scanning
     */
    public function getQrCode(): ?string
    {
        return $this->qrCode;
    }
}
