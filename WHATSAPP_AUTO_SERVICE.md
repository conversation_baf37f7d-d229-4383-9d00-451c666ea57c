# 🚀 WhatsApp Auto Service Management

## 📋 Overview

Sistem WhatsApp sekarang sudah mendukung **auto start/stop service** langsung dari admin panel tanpa perlu manual `npm run dev` di terminal.

## ✨ Fitur Baru

### 🎛️ **Toggle Otomatis**
- **ON**: Otomatis menjalankan `npm run dev` di background
- **OFF**: Otomatis menghentikan service WhatsApp
- **Real-time Status**: Monitoring status service dan koneksi

### 🔧 **Smart Process Management**
- Auto-detect PM2 (jika tersedia)
- Fallback ke direct Node.js process
- Cross-platform support (Windows/Linux/Mac)
- PID tracking untuk process management

## 🎯 Cara Penggunaan

### 1. **Melalui Admin Panel**
1. Login ke admin panel
2. Masuk ke menu **WhatsApp**
3. Toggle **"Aktifkan WhatsApp Service"** → **ON**
4. Service otomatis start, tunggu QR code muncul
5. Scan QR code dengan WhatsApp di HP
6. Status berubah menjadi "Connected" ✅

### 2. **Melalui Command Line** (Opsional)
```bash
# Start service
php artisan whatsapp:service start

# Stop service  
php artisan whatsapp:service stop

# Restart service
php artisan whatsapp:service restart

# Check status
php artisan whatsapp:service status
```

## 🔄 Alur Kerja Sistem

```
Toggle ON → Kill existing processes → Start npm run dev → Monitor PID → Display QR → Scan → Connected
Toggle OFF → Stop process gracefully → Kill if needed → Clean up PID → Disconnected
```

## 🛠️ Technical Implementation

### **Process Management**
- **Primary**: Direct Node.js process dengan Symfony Process
- **Alternative**: PM2 (jika tersedia dan ecosystem.config.js ada)
- **Fallback**: Force kill processes menggunakan port 3001

### **Cross-Platform Commands**
- **Windows**: `cmd /c npm run dev`
- **Linux/Mac**: `npm run dev`
- **PM2**: `pm2 start ecosystem.config.js --only whatsapp-service`

### **Status Monitoring**
- PID file tracking: `storage/app/whatsapp-service.pid`
- API health check: `http://localhost:3001/status`
- Real-time connection status

## 📁 File Structure

```
whatsapp-service/
├── index.js                 # Main Node.js service
├── package.json             # Dependencies
├── ecosystem.config.js      # PM2 config (auto-generated)
├── auth_info_baileys/       # WhatsApp session
└── wa-logs.txt             # Service logs

app/Services/
├── WhatsAppService.php          # API communication
└── WhatsAppProcessService.php   # Process management

app/Console/Commands/
├── WhatsAppServiceManager.php   # CLI management
└── TestWhatsAppNotification.php # Testing
```

## 🚨 Troubleshooting

### **Service Tidak Start**
1. Pastikan Node.js terinstall: `node --version`
2. Pastikan npm terinstall: `npm --version`
3. Check dependencies: `cd whatsapp-service && npm install`
4. Check logs: `storage/logs/laravel.log`

### **QR Code Tidak Muncul**
1. Tunggu 5-10 detik setelah toggle ON
2. Refresh halaman admin
3. Check browser console untuk error
4. Hapus folder `whatsapp-service/auth_info_baileys` untuk reset session

### **Service Tidak Stop**
1. Toggle OFF akan force kill semua process
2. Manual kill: `php artisan whatsapp:service stop`
3. Check running processes: `php artisan whatsapp:service status`

## 🔧 Advanced Configuration

### **Menggunakan PM2 (Production)**
1. Install PM2: `npm install -g pm2`
2. File `ecosystem.config.js` sudah auto-generated
3. Service akan otomatis detect dan gunakan PM2

### **Custom Port**
Edit `whatsapp-service/index.js`:
```javascript
const PORT = process.env.PORT || 3001;
```

Update `app/Services/WhatsAppService.php`:
```php
$this->serverUrl = $this->setting->whatsapp_server_url ?? 'http://localhost:3001';
```

## 📊 Monitoring & Logs

### **Real-time Status**
- Admin panel menampilkan status real-time
- Auto-refresh setiap 5 detik
- Color-coded status indicators

### **Log Files**
- Laravel logs: `storage/logs/laravel.log`
- WhatsApp service logs: `whatsapp-service/wa-logs.txt`
- PM2 logs: `~/.pm2/logs/`

## 🎉 Benefits

✅ **No Manual Terminal**: Tidak perlu buka terminal lagi  
✅ **Auto Management**: Start/stop otomatis via toggle  
✅ **Cross Platform**: Works di Windows, Linux, Mac  
✅ **Smart Detection**: Auto-detect PM2 dan tools lain  
✅ **Real-time Monitoring**: Status dan QR code real-time  
✅ **Error Handling**: Comprehensive error handling  
✅ **Process Cleanup**: Proper process cleanup saat stop  

## 🚀 Next Steps

Sekarang Anda bisa:
1. Toggle WhatsApp service langsung dari admin panel
2. Monitor status real-time
3. Tidak perlu manual terminal lagi
4. Service berjalan otomatis di background

**Happy WhatsApp-ing! 🎉**
