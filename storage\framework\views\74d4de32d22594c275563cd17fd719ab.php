<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Service Status Card -->
    <div class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Status WhatsApp Service
                </h3>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        <?php echo e($isServiceRunning ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                        <?php echo e($isServiceRunning ? 'Running' : 'Stopped'); ?>

                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        <?php echo e($serviceStatus === 'connected' ? 'bg-green-100 text-green-800' :
                           ($serviceStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')); ?>">
                        <?php echo e(ucfirst($serviceStatus)); ?>

                    </span>
                </div>
            </div>

            <!-- QR Code Section Selalu Ada -->
            <div class="border-t pt-4">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">
                    Scan QR Code untuk Menghubungkan WhatsApp
                </h4>
                <div class="flex justify-center">
                    <div class="bg-white p-4 rounded-lg shadow-inner">
                        <div id="qrcode" class="flex justify-center"></div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 text-center mt-3">
                    <!--[if BLOCK]><![endif]--><?php if($serviceStatus === 'connected'): ?>
                        WhatsApp Terhubung! Service siap mengirim notifikasi
                    <?php elseif($serviceStatus === 'connecting'): ?>
                        Service sedang memulai, QR code akan muncul sebentar lagi
                    <?php else: ?>
                        Buka WhatsApp di ponsel Anda → Ketuk menu (⋮) → WhatsApp Web → Scan QR code di atas
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </p>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <?php if (isset($component)) { $__componentOriginald09a0ea6d62fc9155b01d885c3fdffb3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald09a0ea6d62fc9155b01d885c3fdffb3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.form.index','data' => ['wire:submit' => 'submit']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:submit' => 'submit']); ?>
        <?php echo e($this->form); ?>

     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald09a0ea6d62fc9155b01d885c3fdffb3)): ?>
<?php $attributes = $__attributesOriginald09a0ea6d62fc9155b01d885c3fdffb3; ?>
<?php unset($__attributesOriginald09a0ea6d62fc9155b01d885c3fdffb3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald09a0ea6d62fc9155b01d885c3fdffb3)): ?>
<?php $component = $__componentOriginald09a0ea6d62fc9155b01d885c3fdffb3; ?>
<?php unset($__componentOriginald09a0ea6d62fc9155b01d885c3fdffb3); ?>
<?php endif; ?>

    <!-- Auto-refresh QR Code -->
    <!-- Ganti ke versi 1.x agar window.QRCode tersedia (class-based) -->
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        let lastQR = null;
        function renderQR(qrElement, text) {
            if (typeof QRCode === 'function' && typeof QRCode.toCanvas === 'function') {
                QRCode.toCanvas(qrElement, text, {
                    width: 256,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });
            } else if (typeof window.QRCode === 'function') {
                new window.QRCode(qrElement, {
                    text: text,
                    width: 256,
                    height: 256,
                    colorDark: '#000000',
                    colorLight: '#FFFFFF',
                    correctLevel: window.QRCode.CorrectLevel.H
                });
            } else {
                alert('Library QRCode.js belum termuat!');
            }
        }

        function fetchAndRenderQR() {
            fetch('http://localhost:3001/status')
                .then(res => res.json())
                .then(data => {
                    const qrElement = document.getElementById('qrcode');
                    if (qrElement) {
                        if (data.qr && (typeof QRCode !== 'undefined' || typeof window.QRCode !== 'undefined')) {
                            if (lastQR !== data.qr) {
                                qrElement.innerHTML = '';
                                renderQR(qrElement, data.qr);
                                lastQR = data.qr;
                            }
                        } else if (typeof QRCode === 'undefined' && typeof window.QRCode === 'undefined') {
                            setTimeout(fetchAndRenderQR, 1000);
                        } else {
                            qrElement.innerHTML = '';
                            lastQR = null;
                        }
                    }
                });
        }
        fetchAndRenderQR();
        setInterval(fetchAndRenderQR, 5000);
    });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\absensi website\laravel-absensi\resources\views/filament/pages/whats-app.blade.php ENDPATH**/ ?>