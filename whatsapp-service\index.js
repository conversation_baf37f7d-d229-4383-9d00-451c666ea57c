const { 
    default: makeWASocket, 
    DisconnectReason, 
    useMultiFileAuthState,
    fetchLatestBaileysVersion 
} = require('@whiskeysockets/baileys');
const qrcode = require('qrcode-terminal');
const express = require('express');
const cors = require('cors');
const P = require('pino');
const axios = require('axios');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Logger
const logger = P({ timestamp: () => `,"time":"${new Date().toJSON()}"` }, P.destination('./wa-logs.txt'));

// Global variables
let sock;
let qrString = '';
let isConnected = false;
let connectionStatus = 'disconnected';

// Initialize WhatsApp connection
async function connectToWhatsApp() {
    try {
        const { state, saveCreds } = await useMultiFileAuthState('./auth_info_baileys');
        const { version, isLatest } = await fetchLatestBaileysVersion();
        
        console.log(`Using WA v${version.join('.')}, isLatest: ${isLatest}`);
        
        sock = makeWASocket({
            version,
            logger,
            auth: state,
            browser: ['Laravel Absensi System', 'Chrome', '1.0.0']
        });

        sock.ev.on('connection.update', (update) => {
            const { connection, lastDisconnect, qr } = update;
            
            if (qr) {
                qrString = qr;
                console.log('QR Code generated. Ready for admin panel.');
            }
            
            if (connection === 'close') {
                const shouldReconnect = (lastDisconnect?.error)?.output?.statusCode !== DisconnectReason.loggedOut;
                console.log('Connection closed due to ', lastDisconnect?.error, ', reconnecting ', shouldReconnect);
                
                isConnected = false;
                connectionStatus = 'disconnected';
                
                if (shouldReconnect) {
                    connectToWhatsApp();
                }
            } else if (connection === 'open') {
                console.log('WhatsApp connection opened successfully!');
                isConnected = true;
                connectionStatus = 'connected';
                qrString = '';
            }
        });

        sock.ev.on('creds.update', saveCreds);
        
        sock.ev.on('messages.upsert', async (m) => {
            const message = m.messages[0];
            if (!message.key.fromMe && m.type === 'notify') {
                console.log('Received message:', message);
                // Handle incoming messages if needed
            }
        });

    } catch (error) {
        console.error('Error connecting to WhatsApp:', error);
        connectionStatus = 'error';
    }
}

// API Routes
app.get('/status', (req, res) => {
    res.json({
        status: connectionStatus,
        connected: isConnected,
        qr: qrString || null,
        timestamp: new Date().toISOString()
    });
});

app.post('/send-message', async (req, res) => {
    try {
        const { number, message } = req.body;
        
        if (!isConnected) {
            return res.status(400).json({
                success: false,
                error: 'WhatsApp not connected'
            });
        }

        if (!number || !message) {
            return res.status(400).json({
                success: false,
                error: 'Number and message are required'
            });
        }

        // Format number (ensure it has country code)
        let formattedNumber = number.replace(/\D/g, ''); // Remove non-digits
        if (!formattedNumber.startsWith('62')) {
            if (formattedNumber.startsWith('0')) {
                formattedNumber = '62' + formattedNumber.substring(1);
            } else {
                formattedNumber = '62' + formattedNumber;
            }
        }
        formattedNumber += '@s.whatsapp.net';

        // Send message
        await sock.sendMessage(formattedNumber, { text: message });
        
        console.log(`Message sent to ${formattedNumber}: ${message}`);
        
        res.json({
            success: true,
            message: 'Message sent successfully',
            to: formattedNumber,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error sending message:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/logout', async (req, res) => {
    try {
        if (sock) {
            await sock.logout();
            isConnected = false;
            connectionStatus = 'disconnected';
            qrString = '';
        }
        
        res.json({
            success: true,
            message: 'Logged out successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        service: 'WhatsApp Baileys Service',
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`WhatsApp Baileys Service running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/health`);
    console.log(`Status check: http://localhost:${PORT}/status`);
    
    // Initialize WhatsApp connection
    connectToWhatsApp();
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    if (sock) {
        await sock.end();
    }
    process.exit(0);
});
