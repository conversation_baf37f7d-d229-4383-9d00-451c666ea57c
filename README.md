# Laravel Absensi

Sistem manajemen absensi berbasis web menggunakan Laravel dan Filament Admin Panel.

## Fitur

- **Dashboard Admin** - Panel administrasi dengan Filament
- **Manajemen Divisi** - CRUD divisi dengan status aktif/tidak aktif
- **Manajemen Jabatan** - CRUD jabatan dengan status aktif/tidak aktif
- **Real-time Communication** - Integrasi MQTT untuk komunikasi real-time
- **Authentication** - Sistem login kustom
- **Responsive Design** - Interface yang responsif dengan Tailwind CSS

## Teknologi

- **Laravel 11** - Framework PHP
- **Filament 3** - Admin panel
- **Tailwind CSS** - Styling
- **MQTT** - Real-time messaging
- **MySQL/SQLite** - Database

## Instalasi

1. Clone repository
```bash
git clone <repository-url>
cd laravel-absensi
```

2. Install dependencies
```bash
composer install
npm install
```

3. Setup environment
```bash
cp .env.example .env
php artisan key:generate
```

4. Setup database
```bash
php artisan migrate
```

5. Build assets
```bash
npm run build
```

6. Start development server
```bash
composer run dev
```

## Penggunaan

### Admin Panel
Akses admin panel di `/admin` dengan kredensial yang telah dikonfigurasi.

### Fitur Utama
- **Divisi**: Kelola divisi perusahaan
- **Jabatan**: Kelola jabatan karyawan
- **Settings**: Konfigurasi sistem

## Development

### Scripts
- `composer run dev` - Start server, queue, dan vite
- `composer run test` - Jalankan tests
- `npm run dev` - Development mode
- `npm run build` - Production build

### Structure
```
app/
├── Filament/
│   ├── Resources/     # Resource definitions
│   └── Pages/         # Custom pages
resources/
├── css/
└── js/
public/
├── js/filament/       # Filament assets
└── css/filament/      # Filament styles
```

## License

Open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
